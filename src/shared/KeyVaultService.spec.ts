import { getSecretValue } from './KeyVaultService';
import { AzKeyVault } from './AzKeyVault';

jest.mock('./AzKeyVault', () => ({
  AzKeyVault: {
    getSecret: jest.fn(),
  },
}));

describe('KeyVaultService', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getSecretValue', () => {
    it('should return secret value when found', async () => {
      const secretName = 'test-secret';
      const secretValue = 'secret-value';
      (AzKeyVault.getSecret as jest.Mock).mockResolvedValue(secretValue);

      const result = await getSecretValue(secretName);

      expect(AzKeyVault.getSecret).toHaveBeenCalledWith(secretName);
      expect(result).toBe(secretValue);
    });

    it('should handle errors and return undefined', async () => {
      const secretName = 'test-secret';
      const error = new Error('KeyVault error');
      (AzKeyVault.getSecret as jest.Mock).mockRejectedValue(error);
      console.error = jest.fn();

      const result = await getSecretValue(secretName);

      expect(AzKeyVault.getSecret).toHaveBeenCalledWith(secretName);
      expect(console.error).toHaveBeenCalledWith('Error getting secret from KeyVault:', error);
      expect(result).toBeUndefined();
    });

    it('should return undefined when secret value is null', async () => {
      const secretName = 'test-secret';
      (AzKeyVault.getSecret as jest.Mock).mockResolvedValue(null);

      const result = await getSecretValue(secretName);

      expect(AzKeyVault.getSecret).toHaveBeenCalledWith(secretName);
      expect(result).toBeUndefined();
    });

    it('should return undefined when secret value is undefined', async () => {
      const secretName = 'test-secret';
      (AzKeyVault.getSecret as jest.Mock).mockResolvedValue(undefined);

      const result = await getSecretValue(secretName);

      expect(AzKeyVault.getSecret).toHaveBeenCalledWith(secretName);
      expect(result).toBeUndefined();
    });
  });
});
