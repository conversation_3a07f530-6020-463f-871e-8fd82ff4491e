import { RedisService } from './RedisService';
import { createClient } from 'redis';

jest.mock('redis', () => ({
  createClient: jest.fn(),
}));

describe('RedisService', () => {
  let redisService: RedisService;
  let mockRedisClient: any;

  beforeEach(() => {
    mockRedisClient = {
      connect: jest.fn(),
      get: jest.fn(),
      set: jest.fn(),
      del: jest.fn(),
      quit: jest.fn(),
      on: jest.fn(),
    };

    (createClient as jest.Mock).mockReturnValue(mockRedisClient);
    redisService = new RedisService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create Redis client and set up error handler', () => {
      expect(createClient).toHaveBeenCalledWith({
        url: process.env.REDIS_CONNECTION_STRING,
      });
      expect(mockRedisClient.on).toHaveBeenCalledWith('error', expect.any(Function));
    });
  });

  describe('connect', () => {
    it('should connect to Redis', async () => {
      mockRedisClient.connect.mockResolvedValue(undefined);

      await redisService.connect();

      expect(mockRedisClient.connect).toHaveBeenCalled();
    });

    it('should handle connection errors', async () => {
      const error = new Error('Connection failed');
      mockRedisClient.connect.mockRejectedValue(error);
      console.error = jest.fn();

      await redisService.connect();

      expect(console.error).toHaveBeenCalledWith('Redis connection error:', error);
    });
  });

  describe('get', () => {
    it('should get value from Redis', async () => {
      const key = 'test-key';
      const value = 'test-value';
      mockRedisClient.get.mockResolvedValue(value);

      const result = await redisService.get(key);

      expect(mockRedisClient.get).toHaveBeenCalledWith(key);
      expect(result).toBe(value);
    });

    it('should handle get errors', async () => {
      const key = 'test-key';
      const error = new Error('Get failed');
      mockRedisClient.get.mockRejectedValue(error);
      console.error = jest.fn();

      const result = await redisService.get(key);

      expect(console.error).toHaveBeenCalledWith('Redis get error:', error);
      expect(result).toBeNull();
    });
  });

  describe('set', () => {
    it('should set value in Redis', async () => {
      const key = 'test-key';
      const value = 'test-value';
      mockRedisClient.set.mockResolvedValue('OK');

      await redisService.set(key, value);

      expect(mockRedisClient.set).toHaveBeenCalledWith(key, value);
    });

    it('should handle set errors', async () => {
      const key = 'test-key';
      const value = 'test-value';
      const error = new Error('Set failed');
      mockRedisClient.set.mockRejectedValue(error);
      console.error = jest.fn();

      await redisService.set(key, value);

      expect(console.error).toHaveBeenCalledWith('Redis set error:', error);
    });
  });

  describe('del', () => {
    it('should delete value from Redis', async () => {
      const key = 'test-key';
      mockRedisClient.del.mockResolvedValue(1);

      await redisService.del(key);

      expect(mockRedisClient.del).toHaveBeenCalledWith(key);
    });

    it('should handle delete errors', async () => {
      const key = 'test-key';
      const error = new Error('Delete failed');
      mockRedisClient.del.mockRejectedValue(error);
      console.error = jest.fn();

      await redisService.del(key);

      expect(console.error).toHaveBeenCalledWith('Redis del error:', error);
    });
  });

  describe('disconnect', () => {
    it('should disconnect from Redis', async () => {
      mockRedisClient.quit.mockResolvedValue('OK');

      await redisService.disconnect();

      expect(mockRedisClient.quit).toHaveBeenCalled();
    });

    it('should handle disconnect errors', async () => {
      const error = new Error('Disconnect failed');
      mockRedisClient.quit.mockRejectedValue(error);
      console.error = jest.fn();

      await redisService.disconnect();

      expect(console.error).toHaveBeenCalledWith('Redis disconnect error:', error);
    });
  });
});
